package com.domino.common.elasticsearch;

import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * ES逻辑删除工具类
 * 提供通用的逻辑删除操作方法
 */
@Slf4j
@Component
public class LogicalDeleteUtil {

    @Resource
    private RestHighLevelClient restHighLevelClient;

    /**
     * 逻辑删除字段名
     */
    public static final String LOGICAL_DELETE_FIELD = "isDeleted";

    /**
     * 未删除状态值
     */
    public static final Integer NOT_DELETED = 0;

    /**
     * 已删除状态值
     */
    public static final Integer DELETED = 1;

    /**
     * 根据ID逻辑删除文档
     *
     * @param index      索引名称
     * @param documentId 文档ID
     * @throws IOException IO异常
     */
    public void logicalDeleteById(String index, String documentId) throws IOException {
        Map<String, Object> updateDoc = new HashMap<>();
        updateDoc.put(LOGICAL_DELETE_FIELD, DELETED);
        updateDoc.put("updateTime", new Date());

        UpdateRequest updateRequest = new UpdateRequest(index, documentId).doc(updateDoc);
        restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
    }

    /**
     * 根据查询条件批量逻辑删除文档
     *
     * @param index      索引名称
     * @param boolQuery  查询条件
     * @param maxSize    最大处理数量
     * @return 删除的文档数量
     * @throws IOException IO异常
     */
    public long logicalDeleteByQuery(String index, BoolQueryBuilder boolQuery, int maxSize) throws IOException {
        // 添加未删除条件，避免重复删除
        boolQuery.must(QueryBuilders.termQuery(LOGICAL_DELETE_FIELD, NOT_DELETED));

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(maxSize);

        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.source(searchSourceBuilder);

        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

        if (searchResponse.getHits().getTotalHits().value > 0) {
            BulkRequest bulkRequest = new BulkRequest();

            searchResponse.getHits().forEach(hit -> {
                Map<String, Object> updateDoc = new HashMap<>();
                updateDoc.put(LOGICAL_DELETE_FIELD, DELETED);
                updateDoc.put("updateTime", new Date());

                UpdateRequest updateRequest = new UpdateRequest(index, hit.getId()).doc(updateDoc);
                bulkRequest.add(updateRequest);
            });

            restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
            long deletedCount = searchResponse.getHits().getTotalHits().value;
            return deletedCount;
        }

        return 0;
    }

    /**
     * 为查询条件添加逻辑删除过滤
     *
     * @param boolQuery 查询条件
     */
    public void addLogicalDeleteFilter(BoolQueryBuilder boolQuery) {
        boolQuery.filter(QueryBuilders.termQuery(LOGICAL_DELETE_FIELD, NOT_DELETED));
    }

    /**
     * 为查询条件添加逻辑删除过滤（包含已删除数据）
     *
     * @param boolQuery     查询条件
     * @param includeDeleted 是否包含已删除数据
     */
    public void addLogicalDeleteFilter(BoolQueryBuilder boolQuery, boolean includeDeleted) {
        if (!includeDeleted) {
            addLogicalDeleteFilter(boolQuery);
        }
    }

    /**
     * 恢复逻辑删除的文档
     *
     * @param index      索引名称
     * @param documentId 文档ID
     * @throws IOException IO异常
     */
    public void restoreLogicalDeletedDocument(String index, String documentId) throws IOException {
        Map<String, Object> updateDoc = new HashMap<>();
        updateDoc.put(LOGICAL_DELETE_FIELD, NOT_DELETED);
        updateDoc.put("updateTime", new Date());

        UpdateRequest updateRequest = new UpdateRequest(index, documentId).doc(updateDoc);
        restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
    }
}
