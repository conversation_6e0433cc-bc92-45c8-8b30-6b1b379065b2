package com.domino.qh.listener;

import com.domino.common.event.AbstractQhEventListener;
import com.domino.common.event.QuestionEvent;
import com.domino.common.exception.qh.QhException;
import com.domino.common.qh.document.QuestionDocument;
import com.domino.qh.service.QuestionElasticSearchService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationEvent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 题目事件监听器
 * 继承AbstractQhEventListener，专门处理题目相关的ES操作事件
 */
@Component
@Slf4j
public class QuestionEventListener extends AbstractQhEventListener {
    
    @Resource
    private QuestionElasticSearchService questionElasticSearchService;
    
    @Override
    public boolean supportsEventType(Class<? extends ApplicationEvent> eventType) {
        return eventType == QuestionEvent.class;
    }
    
    @Override
    public int getOrder() {
        return 0;
    }
    
    @Override
    protected void handleEvent(ApplicationEvent applicationEvent) {
        QuestionEvent event = castEvent(applicationEvent, QuestionEvent.class);
        if (event == null) {
            log.warn("[题目事件] 接收到非QuestionEvent类型的事件，忽略处理");
            return;
        }
        
        switch (event.getEventType()) {
            case INSERT:
                handleInsert(event);
                break;
            case UPDATE:
                handleUpdate(event);
                break;
            case DELETE:
                handleDelete(event);
                break;
            case BATCH_DELETE:
                handleBatchDelete(event);
                break;
            case BATCH_INSERT:
                handleBatchInsert(event);
                break;
            case REBUILD_INDEX:
                handleRebuildIndex(event);
                break;
            case SYNC_DATA:
                handleSyncData(event);
                break;
            default:
                log.warn("[题目事件] 不支持的事件类型: {}, 该事件将被忽略", event.getEventType());
                break;
        }
    }
    
    @Override
    protected void handleEventError(ApplicationEvent event, Exception e) {
        QuestionEvent questionEvent = castEvent(event, QuestionEvent.class);
        if (questionEvent != null) {
            // 可以考虑添加重试机制或者将失败的事件记录到数据库中
        }
    }
    
    @Override
    protected String getListenerName() {
        return "题目事件监听器";
    }
    
    @Override
    protected String getEventDescription(ApplicationEvent event) {
        QuestionEvent questionEvent = castEvent(event, QuestionEvent.class);
        if (questionEvent != null) {
            return String.format("QuestionEvent{type=%s, operateBy=%s, operateTimestamp=%d}",
                questionEvent.getEventType(), questionEvent.getOperateBy(), questionEvent.getOperateTimestamp());
        }
        return super.getEventDescription(event);
    }
    
    /**
     * 处理新增事件
     */
    private void handleInsert(QuestionEvent event) {
        try {
            QuestionDocument questionDocument = event.getQuestionDocument();
            if (questionDocument == null) {
                throw new QhException("[题目事件] 新增事件缺少题目文档数据");
            }
            String documentId = questionElasticSearchService.saveQuestion(questionDocument);
            log.info("[题目事件] 题目新增成功, 题目ID: {}, ES文档ID: {}", questionDocument.getId(), documentId);
        } catch (Exception e) {
            throw new QhException("[题目事件] 处理新增事件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 处理更新事件
     */
    private void handleUpdate(QuestionEvent event) {
        try {
            QuestionDocument questionDocument = event.getQuestionDocument();
            if (questionDocument == null) {
                throw new QhException("[题目事件] 更新事件缺少题目文档数据");
            }
            
            questionElasticSearchService.updateQuestion(questionDocument);
            log.info("[题目事件] 题目更新成功, 题目ID: {}", questionDocument.getId());
        } catch (Exception e) {
            throw new QhException("[题目事件] 处理更新事件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 处理删除事件
     */
    private void handleDelete(QuestionEvent event) {
        try {
            String questionId = event.getQuestionId();
            if (questionId == null) {
                throw new QhException("[题目事件] 删除事件缺少题目ID");
            }
            
            questionElasticSearchService.deleteQuestionById(questionId);
            log.info("[题目事件] 题目删除成功, 题目ID: {}", questionId);
        } catch (Exception e) {
            throw new QhException("[题目事件] 处理删除事件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 处理批量删除事件
     */
    private void handleBatchDelete(QuestionEvent event) {
        try {
            List<String> questionIds = event.getQuestionIds();
            if (CollectionUtils.isEmpty(questionIds)) {
                throw new QhException("[题目事件] 批量删除事件缺少题目ID列表");
            }
            
            int successCount = 0;
            int failCount = 0;
            
            for (String questionId : questionIds) {
                try {
                    questionElasticSearchService.deleteQuestionById(questionId);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                }
            }
            
            log.info("[题目事件] 批量删除完成, 成功: {}, 失败: {}, 总数: {}", successCount, failCount, questionIds.size());
        } catch (Exception e) {
            throw new QhException("[题目事件] 处理批量删除事件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 处理批量新增事件
     */
    private void handleBatchInsert(QuestionEvent event) {
        try {
            List<QuestionDocument> questionDocuments = event.getQuestionDocuments();
            if (CollectionUtils.isEmpty(questionDocuments)) {
                throw new QhException("[题目事件] 批量新增事件缺少题目文档列表");
            }
            
            int successCount = 0;
            int failCount = 0;
            
            for (QuestionDocument questionDocument : questionDocuments) {
                try {
                    questionElasticSearchService.saveQuestion(questionDocument);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                }
            }

            log.info("[题目事件] 批量新增完成, 成功: {}, 失败: {}, 总数: {}", successCount, failCount, questionDocuments.size());
        } catch (Exception e) {
            throw new QhException("[题目事件] 处理批量新增事件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 处理重建索引事件
     */
    private void handleRebuildIndex(QuestionEvent event) {
        try {
            // 删除现有索引
            if (questionElasticSearchService.isQuestionIndexExist()) {
                questionElasticSearchService.deleteQuestionIndex();
                log.info("[题目事件] 已删除现有索引");
            }
            
            // 创建新索引
            boolean created = questionElasticSearchService.createQuestionIndex();
            if (created) {
                log.info("[题目事件] 重建索引成功");
            } else {
                log.warn("[题目事件] 重建索引失败");
            }
        } catch (Exception e) {
            throw new QhException("[题目事件] 处理重建索引事件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 处理同步数据事件
     */
    private void handleSyncData(QuestionEvent event) {
        try {
            Object extraData = event.getExtraData();
            log.info("[题目事件] 开始同步数据, 额外参数: {}", extraData);
            
            // 这里可以实现具体的数据同步逻辑
            // 比如从数据库重新加载所有题目到ES
            
            log.info("[题目事件] 数据同步完成");
        } catch (Exception e) {
            throw new QhException("[题目事件] 处理同步数据事件失败: {}", e.getMessage());
        }
    }
}
