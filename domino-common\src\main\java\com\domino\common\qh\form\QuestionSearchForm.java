package com.domino.common.qh.form;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 题目搜索表单
 */
@Data
public class QuestionSearchForm implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关键词搜索（在题干和解析中搜索）
     */
    private String keyword;

    /**
     * 题目类型列表
     */
    private List<String> questionTypes;

    /**
     * 难度列表
     */
    private List<String> difficulties;

    /**
     * 试卷类型列表
     */
    private List<String> paperTypes;

    /**
     * 年份列表
     */
    private List<String> years;

    /**
     * 区域列表
     */
    private List<String> regions;

    /**
     * 区域列表
     */
    private String region;

    /**
     * 年级ID列表
     */
    private List<String> gradeIds;

    /**
     * 学科列表
     */
    private List<String> subjects;

    /**
     * 知识点ID列表
     */
    private List<String> knowledgeTreeIds;

    /**
     * 试卷名称
     */
    private String paperName;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 试卷来源列表
     */
    private List<String> paperSources;

    /**
     * 试题标签列表
     */
    private List<String> tags;

    /**
     * 知识点关键词搜索
     */
    private String knowledgeKeyword;

    /**
     * 分数范围 - 最小值
     */
    private Integer minScore;

    /**
     * 分数范围 - 最大值
     */
    private Integer maxScore;

    /**
     * 题目分类 0-题目 1-答案或解析
     */
    private Integer category;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    private String sortField = "createTime";

    /**
     * 排序方向 asc/desc
     */
    private String sortOrder = "desc";

    /**
     * 高亮字段
     */
    private List<String> highlightFields;
}
