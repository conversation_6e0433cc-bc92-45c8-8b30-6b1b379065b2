package com.domino.qh.service.impl;

import com.domino.common.qh.domain.QhExamPaper;
import com.domino.common.qh.domain.QhQuestionBank;
import com.domino.common.qh.domain.QhKnowledgeTree;
import com.domino.common.qh.dto.QhPaperAnalysisDTO;
import com.domino.common.qh.dto.QhPaperAnalysisDTO.PieChartItem;
import com.domino.qh.mapper.QhExamPaperMapper;
import com.domino.qh.mapper.QhKnowledgeQuestionMapper;
import com.domino.qh.service.IQhPaperAnalysisService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 试卷分析服务实现
 */
@Service
public class QhPaperAnalysisServiceImpl implements IQhPaperAnalysisService {

    @Autowired
    private QhExamPaperMapper examPaperMapper;
    
    @Autowired
    private QhKnowledgeQuestionMapper knowledgeQuestionMapper;
    
    /**
     * 难度级别映射
     */
    private static final Map<String, String> DIFFICULTY_MAP = new HashMap<>();
    static {
        DIFFICULTY_MAP.put("1", "简单");
        DIFFICULTY_MAP.put("2", "正常");
        DIFFICULTY_MAP.put("3", "困难");
        DIFFICULTY_MAP.put("4", "挑战");
    }
    
    /**
     * 题型映射
     */
    private static final Map<String, String> QUESTION_TYPE_MAP = new HashMap<>();
    static {
        QUESTION_TYPE_MAP.put("1", "单选题");
        QUESTION_TYPE_MAP.put("2", "多选题");
        QUESTION_TYPE_MAP.put("3", "判断题");
        QUESTION_TYPE_MAP.put("4", "解答题");
        QUESTION_TYPE_MAP.put("5", "填空题");
    }

    /**
     * 生成试卷分析报告
     * @param paperId 试卷ID
     * @param questionBanks 试卷包含的题目列表
     * @return 试卷分析报告DTO
     */
    @Override
    public QhPaperAnalysisDTO generatePaperAnalysisReport(String paperId, List<QhQuestionBank> questionBanks) {
        if (CollectionUtils.isEmpty(questionBanks)) {
            return null;
        }

        // 获取试卷基本信息
        QhExamPaper paper = examPaperMapper.selectQhExamPaperById(paperId);
        if (paper == null) {
            return null;
        }

        QhPaperAnalysisDTO report = new QhPaperAnalysisDTO();
        report.setPaperName(paper.getPaperName());
        report.setPaperType(paper.getPaperType());
        
        // 题目总数
        int totalQuestions = questionBanks.size();
        report.setTotalQuestions(totalQuestions);
        
        // 计算总分
        BigDecimal totalScore = BigDecimal.ZERO;
        for (QhQuestionBank question : questionBanks) {
            if (question.getScore() != null) {
                totalScore = totalScore.add(new BigDecimal(question.getScore()));
            }
        }
        report.setTotalScore(totalScore);
        
        // 统计知识点覆盖情况
        Map<String, String> knowledgePointMap = new HashMap<>();
        for (QhQuestionBank question : questionBanks) {
            // 获取题目关联的知识点
            List<QhKnowledgeTree> knowledgePoints = knowledgeQuestionMapper.selectKnowledgeTreesByQuestionId(question.getId());
            if (CollectionUtils.isNotEmpty(knowledgePoints)) {
                for (QhKnowledgeTree knowledgePoint : knowledgePoints) {
                    knowledgePointMap.put(knowledgePoint.getId(), knowledgePoint.getName());
                }
            }
        }
        report.setKnowledgePointMap(knowledgePointMap);
        report.setKnowledgePointCount(knowledgePointMap.size());
        
        // 假设满分知识点覆盖为100个，这可以从系统配置或总知识点数量中获取
        int totalKnowledgePoints = 100; // 示例值，实际应从配置获取
        BigDecimal knowledgeCoverage = BigDecimal.valueOf(knowledgePointMap.size())
                .divide(BigDecimal.valueOf(totalKnowledgePoints), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
        report.setKnowledgeCoverageRate(knowledgeCoverage);
        
        // 统计难度分布（使用中文描述）
        Map<String, Integer> difficultyMap = new HashMap<>();
        for (QhQuestionBank question : questionBanks) {
            String difficultyCode = question.getDifficulty();
            // 将数字编码转换为中文描述
            String difficultyName = DIFFICULTY_MAP.getOrDefault(difficultyCode, difficultyCode);
            Integer count = difficultyMap.get(difficultyName);
            if (count == null) {
                count = 0;
            }
            difficultyMap.put(difficultyName, count + 1);
        }
        report.setDifficultyMap(difficultyMap);
        
        // 计算各难度百分比
        int easyCount = difficultyMap.getOrDefault(DIFFICULTY_MAP.get("1"), 0);
        int mediumCount = difficultyMap.getOrDefault(DIFFICULTY_MAP.get("2"), 0);
        int hardCount = difficultyMap.getOrDefault(DIFFICULTY_MAP.get("3"), 0);
        int challengeCount = difficultyMap.getOrDefault(DIFFICULTY_MAP.get("4"), 0);
        
        BigDecimal easyPercentage = calculatePercentage(easyCount, totalQuestions);
        BigDecimal mediumPercentage = calculatePercentage(mediumCount, totalQuestions);
        BigDecimal hardPercentage = calculatePercentage(hardCount, totalQuestions);
        BigDecimal challengePercentage = calculatePercentage(challengeCount, totalQuestions);
        
        report.setEasyPercentage(easyPercentage);
        report.setMediumPercentage(mediumPercentage);
        report.setHardPercentage(hardPercentage);
        report.setChallengePercentage(challengePercentage);
        
        // 准备难度饼图数据（使用中文描述）
        List<PieChartItem> difficultyPieData = new ArrayList<>();
        if (easyCount > 0) {
            difficultyPieData.add(new PieChartItem(DIFFICULTY_MAP.get("1"), BigDecimal.valueOf(easyCount), easyPercentage));
        }
        if (mediumCount > 0) {
            difficultyPieData.add(new PieChartItem(DIFFICULTY_MAP.get("2"), BigDecimal.valueOf(mediumCount), mediumPercentage));
        }
        if (hardCount > 0) {
            difficultyPieData.add(new PieChartItem(DIFFICULTY_MAP.get("3"), BigDecimal.valueOf(hardCount), hardPercentage));
        }
        // 添加挑战难度
        if (challengeCount > 0) {
            difficultyPieData.add(new PieChartItem(DIFFICULTY_MAP.get("4"), BigDecimal.valueOf(challengeCount), challengePercentage));
        }
        report.setDifficultyPieData(difficultyPieData);
        
        // 统计题型分布（使用中文描述）
        Map<String, Integer> questionTypeMap = new HashMap<>();
        for (QhQuestionBank question : questionBanks) {
            String questionTypeCode = question.getQuestionType();
            // 将数字编码转换为中文描述
            String questionTypeName = QUESTION_TYPE_MAP.getOrDefault(questionTypeCode, questionTypeCode);
            Integer count = questionTypeMap.get(questionTypeName);
            if (count == null) {
                count = 0;
            }
            questionTypeMap.put(questionTypeName, count + 1);
        }
        report.setQuestionTypeMap(questionTypeMap);
        
        // 格式化题型分布文本（使用中文描述）
        StringBuilder typeDistribution = new StringBuilder();
        for (Map.Entry<String, Integer> entry : questionTypeMap.entrySet()) {
            BigDecimal percentage = calculatePercentage(entry.getValue(), totalQuestions);
            typeDistribution.append(entry.getKey())
                    .append(": ")
                    .append(percentage)
                    .append("%, ");
        }
        if (typeDistribution.length() > 2) {
            typeDistribution.setLength(typeDistribution.length() - 2);
        }
        report.setQuestionTypeDistribution(typeDistribution.toString());
        
        // 准备题型饼图数据（使用中文描述）
        List<PieChartItem> questionTypePieData = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : questionTypeMap.entrySet()) {
            BigDecimal percentage = calculatePercentage(entry.getValue(), totalQuestions);
            questionTypePieData.add(new PieChartItem(entry.getKey(), 
                    BigDecimal.valueOf(entry.getValue()), percentage));
        }
        report.setQuestionTypePieData(questionTypePieData);
        
        return report;
    }
    
    /**
     * 计算百分比
     */
    private BigDecimal calculatePercentage(int value, int total) {
        if (total == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(value)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(total), 2, RoundingMode.HALF_UP);
    }
} 