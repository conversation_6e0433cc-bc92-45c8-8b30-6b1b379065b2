# 项目相关配置
domino:
  # 名称
  name: Domino
  # 版本
  version: 1.0.1
  # 版权年份
  copyrightYear: 2024
  # 文件路径 示例（ Windows配置D:/dominoFile/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/dominoFile/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

server:
  port: 8200
  servlet:
    context-path: /domino
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

spring:
  application:
    name: dominoAdmin
  task:
    execution:
      pool:
        core-size: 2
        max-size: 5
        queue-capacity: 100
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 设置总上传的文件大小
      max-request-size: 200MB
  redis:
    # 地址
    host: ${REDIS_HOST:domino-admin.site}
    # 端口，默认为6379
    port: ${REDIS_PORT:2648}
    # 数据库索引
    database: 0
    # 密码
    password: ${REDIS_PWD:1qaz@WSX}
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  #  kafka:
  #    bootstrap-servers: http://${KAFKA_HOST:*************}:9092
  #    producer:
  #      #这个参数可以是任意字符串，它是broker用来识别消息是来自哪个客户端的。在broker进行打印日志、衡量指标或者配额限制时会用到。
  #      clientId: ${spring.application.name} #方便kafkaserver打印日志定位请求来源
  #      acks: all
  #      batch-size: 16384
  #      buffer-memory: 33554432
  #      key-serializer: org.apache.kafka.common.serialization.StringSerializer
  #      value-serializer: org.apache.kafka.common.serialization.StringSerializer
  #      retries: 3
  #      properties:
  #        #开启幂等性
  #        enable.idempotence: true
  #    consumer:
  #      group-id: ikun
  #      #消费方式: 在有提交记录的时候，earliest与latest是一样的，从提交记录的下一条开始消费
  #      #earliest：无提交记录，从头开始消费
  #      #latest：无提交记录，从最新的消息的下一条开始消费
  #      auto-offset-reset: earliest
  #      enable-auto-commit: true #是否自动提交偏移量offset
  #      auto-commit-interval: 1s #前提是 enable-auto-commit=true。自动提交的频率
  #      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
  #      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
  #      max-poll-records: 2
  #      properties:
  #        #如果在这个时间内没有收到心跳，该消费者会被踢出组并触发{组再平衡 rebalance}
  #        session.timeout.ms: 120000
  #        #最大消费时间。此决定了获取消息后提交偏移量的最大时间，超过设定的时间（默认5分钟），服务端也会认为该消费者失效。踢出并再平衡
  #        max.poll.interval.ms: 300000
  #        #配置控制客户端等待请求响应的最长时间。
  #        #如果在超时之前没有收到响应，客户端将在必要时重新发送请求，
  #        #或者如果重试次数用尽，则请求失败。
  #        request.timeout.ms: 60000
  #        #订阅或分配主题时，允许自动创建主题。0.11之前，必须设置false
  #        allow.auto.create.topics: true
  #        #poll方法向协调器发送心跳的频率，为session.timeout.ms的三分之一
  #        heartbeat.interval.ms: 40000
  #        #每个分区里返回的记录最多不超max.partitions.fetch.bytes 指定的字节
  #        #0.10.1版本后 如果 fetch 的第一个非空分区中的第一条消息大于这个限制
  #        #仍然会返回该消息，以确保消费者可以进行
  #        #max.partition.fetch.bytes=1048576  #1M
  #    listener:
  #      #当enable.auto.commit的值设置为false时，该值会生效；为true时不会生效
  #      #manual_immediate:需要手动调用Acknowledgment.acknowledge()后立即提交
  #      #ack-mode: manual_immediate
  #      missing-topics-fatal: false #如果至少有一个topic不存在，true启动失败。false忽略
  #      #type: single #单条消费？批量消费？ #批量消费需要配合 consumer.max-poll-records
  #      type: batch
  #      concurrency: 2 #配置多少，就为为每个消费者实例创建多少个线程。多出分区的线程空闲
  # es集群名称
  elasticsearch:
    clusterName: single-node-cluster
    #es用户名
    userName: elastic
    #es密码
    password: elastic
    # es host ip 地址(集群)：本次使用的是单机模式
    hosts: ${ELASTICSEARCH_HOST:*************}:9200
    # es 请求方式
    scheme: http
    # es 连接超时时间
    connectTimeOut: 1000
    # es socket 连接超时时间
    socketTimeOut: 30000
    # es 请求超时时间
    connectionRequestTimeOut: 500
    # es 最大连接数
    maxConnectNum: 100
    # es 每个路由的最大连接数
    maxConnectNumPerRoute: 100

# Elasticsearch配置
elasticsearch:
  # ES映射配置
  mappings:
    question:
      enabled: true
      index-name: qh_questions
      number-of-shards: 1
      number-of-replicas: 1
      properties:
        id:
          type: keyword
        gradeId:
          type: keyword
        questionType:
          type: keyword
        questionTypeName:
          type: keyword
        context:
          type: text
        ocrText:
          type: text
          analyzer: ik_max_word
          search-analyzer: ik_smart
          fields:
            keyword:
              type: keyword
        questionAnalyze:
          type: text
          analyzer: ik_max_word
          search-analyzer: ik_smart
          fields:
            keyword:
              type: keyword
        score:
          type: keyword
          ignore_above: 50
        difficulty:
          type: keyword
          ignore_above: 50
        paperType:
          type: keyword
          ignore_above: 100
        sourcePaper:
          type: keyword
          ignore_above: 200
        year:
          type: keyword
          ignore_above: 10
        region:
          type: keyword
          ignore_above: 100
        subject:
          type: keyword
          ignore_above: 100
        tag:
          type: text
          analyzer: ik_max_word
          search_analyzer: ik_smart
          fields:
            keyword:
              type: keyword
              ignore_above: 200
        createBy:
          type: keyword
        createTime:
          type: date
          format: "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
        updateBy:
          type: keyword
        updateTime:
          type: date
          format: "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
        knowledgeTreeIds:
          type: keyword
        allKnowledgeTreeIds:
          type: keyword
        allKnowledgeTreeNames:
          type: text
          analyzer: ik_max_word
          search_analyzer: ik_smart
          fields:
            keyword:
              type: keyword
        knowledgeTreeNames:
          type: text
          analyzer: ik_max_word
          search-analyzer: ik_smart
          fields:
            keyword:
              type: keyword
        paperName:
          type: text
          analyzer: ik_max_word
          search-analyzer: ik_smart
          fields:
            keyword:
              type: keyword
        questionIndex:
          type: integer
        category:
          type: integer
        x1:
          type: double
        y1:
          type: double
        y4:
          type: double
        isDeleted:
          type: integer
      analyzers:
        ik_max_word:
          type: ik_max_word
        ik_smart:
          type: ik_smart

mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.domino.**.domain
  configuration:
    # 开启MyBatisPlus的日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    #开启驼峰命名：默认开启
    map-underscore-to-camel-case: true
    #开启二级缓存，默认开启
    cache-enabled: true
  global-config:
    banner: false
    db-config:
      id-type: assign_id
      logic-delete-field: isDelete # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)

zookeeper:
  connect-string: ${ZK_HOST:domino-admin.site}:${ZK_PORT:4262}  # ZooKeeper集群地址
  username: domino
  password: 1qaz@WSX

minio:
  endpoint: http://${MINIO_HOST:*************}:9000 #Minio服务所在地址
  fileHost: http://${MINIO_HOST:*************}:9000 # 文件地址host
  bucketName: qh-test #存储桶名称
  accessKey: cO15KrzoAoY87ovjtfCP #访问的key
  secretKey: aGm0vdSRcyOJbqZObGdNtKeDWlzNOkWwpg0yDvje #访问的秘钥
  imgSize: 102400 # 图片大小限制，单位：m
  fileSize: 102400 # 文件大小限制，单位：m

logging:
  level:
    # 记录mybatisplus日志到文件中
    com.baomidou.mybatisplus: INFO
    #项目mapper目录
    com.domino.web.mapper: DEBUG

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 1

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: zhiyinnitaimei
  # 令牌有效期（默认30分钟）
  expireTime: 30

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# ocr图像识别
ocr:
  tesseract:
    # windows环境
    datapath: D:\OCR\tessdata
    # linu环境？
  aliyun:
    appCode: 2d045e48fac340d2a7c1d523c7ba8331



paper:
  storage:
    path: ./papers

# 阿里云OCR配置
aliyun:
  ocr:
    accessKeyId: your-access-key-id
    accessKeySecret: your-access-key-secret
    endpoint: https://ocr.cn-shanghai.aliyuncs.com
