package com.domino.qh.controller;

import com.domino.common.annotation.Log;
import com.domino.common.core.controller.BaseController;
import com.domino.common.core.domain.AjaxResult;
import com.domino.common.core.page.TableDataInfo;
import com.domino.common.enums.BusinessType;
import com.domino.common.qh.domain.QhExamPaper;
import com.domino.common.qh.domain.QhQuestionBank;
import com.domino.common.qh.dto.QhGeneratePaperDTO;
import com.domino.common.qh.dto.QhPaperAnalysisDTO;
import com.domino.common.qh.dto.QhQuestionBankDTO;
import com.domino.common.utils.PaperReportPdfExporter;
import com.domino.common.utils.poi.ExcelUtil;
import com.domino.qh.service.IQhExamPaperService;
import com.domino.qh.service.IQhPaperAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.List;

/**
 * 试卷分类Controller
 *
 * <AUTHOR>
 * @date 2025-04-21
 */
@RestController
@RequestMapping("/qh/paper")
public class QhExamPaperController extends BaseController {
    @Autowired
    private IQhExamPaperService qhExamPaperService;

    @Autowired
    private IQhPaperAnalysisService qhPaperAnalysisService;

    /**
     * 自动试卷
     */
    @PostMapping("/test")
    @Log(title = "自动试卷", businessType = BusinessType.INSERT)
    public AjaxResult upload(@RequestBody QhGeneratePaperDTO paperDTO) {
        List<QhQuestionBank> questionBankList = qhExamPaperService.generatePaper(paperDTO);
        return success(questionBankList);
    }

    /**
     * 平行组卷
     */
    @GetMapping(value = "/pingxing/{id}")
    @Log(title = "平行试卷", businessType = BusinessType.INSERT)
    public AjaxResult pingxing(@PathVariable("id") String id, String flag) {
        List<QhQuestionBank> questionBankList = qhExamPaperService.pingxing(id, flag);
        return success(questionBankList);
    }

    /**
     * 首页统计数据
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/dashboard")
    public AjaxResult dashboard(String beginTime, String endTime) {
        return success(qhExamPaperService.dashboard(beginTime, endTime));
    }

    /**
     * 查询试卷列表
     */
    @PreAuthorize("@ss.hasPermi('qh:paper:list')")
    @GetMapping("/list")
    public TableDataInfo list(QhExamPaper qhExamPaper) {
        startPage();
        List<QhExamPaper> list = qhExamPaperService.selectQhExamPaperList(qhExamPaper);
        return getDataTable(list);
    }

    /**
     * 导出试卷列表
     */
    @PreAuthorize("@ss.hasPermi('qh:paper:export')")
    @Log(title = "试卷导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export/report")
    public void export(HttpServletResponse response, String id, String fileType) {
//        try {
//            // 设置响应头，防止中文乱码
//            response.setCharacterEncoding("utf-8");
//
//            // 获取试卷题目列表
//            List<QhQuestionBank> questionBanks = qhExamPaperService.selectQhExamPaperById(id);
//            if (questionBanks == null || questionBanks.isEmpty()) {
//                logger.error("导出失败：未找到试卷题目，ID: {}", id);
//                response.setContentType("text/html;charset=utf-8");
//                response.getWriter().write("未找到试卷题目数据");
//                return;
//            }
//
//            // 根据fileType选择不同的导出方式
//            if ("pdf".equalsIgnoreCase(fileType)) {
//                // 生成分析报告数据
//                QhPaperAnalysisDTO analysisReport = qhPaperAnalysisService.generatePaperAnalysisReport(id, questionBanks);
//                if (analysisReport != null) {
//                    // 导出PDF报告
//                    PaperReportPdfExporter.exportPdf(response, analysisReport);
//                } else {
//                    logger.error("导出PDF失败：无法生成分析报告数据，ID: {}", id);
//                    response.setContentType("text/html;charset=utf-8");
//                    response.getWriter().write("无法生成分析报告数据");
//                }
//            } else {
//                // 默认导出Excel
//                // 生成分析报告数据
//                QhPaperAnalysisDTO analysisReport = qhPaperAnalysisService.generatePaperAnalysisReport(id, questionBanks);
//                if (analysisReport != null) {
//                    // 使用ExcelUtil导出Excel
//                    ExcelUtil<QhPaperAnalysisDTO> util = new ExcelUtil<>(QhPaperAnalysisDTO.class);
//                    util.exportExcel(response, Collections.singletonList(analysisReport), "试卷分析报告");
//                } else {
//                    logger.error("导出Excel失败：无法生成分析报告数据，ID: {}", id);
//                    response.setContentType("text/html;charset=utf-8");
//                    response.getWriter().write("无法生成分析报告数据");
//                }
//            }
//        } catch (Exception e) {
//            logger.error("导出试卷报告失败", e);
//            try {
//                response.setContentType("text/html;charset=utf-8");
//                response.getWriter().write("导出失败：" + e.getMessage());
//            } catch (IOException ex) {
//                logger.error("写入错误响应失败", ex);
//            }
//        }
    }

    /**
     * 获取试卷
     */
    @PreAuthorize("@ss.hasPermi('qh:paper:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(qhExamPaperService.queryQhExamPaperById(id));
    }

    /**
     * 获取试卷详细信息
     */
    @PreAuthorize("@ss.hasPermi('qh:paper:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfoById(@PathVariable("id") String id, String flag) {
        return success(qhExamPaperService.selectQhExamPaperById(id, flag));
    }

    /**
     * 新增试卷
     */
    @PreAuthorize("@ss.hasPermi('qh:paper:add')")
    @Log(title = "试卷创建", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QhGeneratePaperDTO paperDTO) {
        return toAjax(qhExamPaperService.insertQhExamPaper(paperDTO));
    }

    /**
     * 修改试卷分类
     */
    @PreAuthorize("@ss.hasPermi('qh:paper:edit')")
    @Log(title = "修改试卷", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QhExamPaper qhExamPaper) {
        return toAjax(qhExamPaperService.updateQhExamPaper(qhExamPaper));
    }

    /**
     * 删除试卷,根据组卷和试卷列表的删除标记来判断删除
     */
    @PreAuthorize("@ss.hasPermi('qh:paper:remove')")
    @Log(title = "删除试卷", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids, String flag) {
        return toAjax(qhExamPaperService.deleteQhExamPaperByIds(ids, flag));
    }
}
