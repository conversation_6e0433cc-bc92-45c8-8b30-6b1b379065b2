package com.domino.common.qh.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ES题目文档实体类
 * 用于存储题目信息到Elasticsearch
 */
@Data
public class QuestionDocument implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 题目ID
     */
    private String id;

    /**
     * 年级ID
     */
    private String gradeId;

    /**
     * 题目类型 1-单选题 2-多选题 3-判断题 4-解答题 5-填空题
     */
    private String questionType;

    /**
     * 题目类型名称
     */
    private String questionTypeName;

    /**
     * 题干内容(图片URL)
     */
    private String context;

    /**
     * OCR解析的题干文本内容
     */
    private String ocrText;

    /**
     * 题目解析(答案)
     */
    private String questionAnalyze;

    /**
     * 分数
     */
    private String score;

    /**
     * 难度
     */
    private String difficulty;

    /**
     * 试卷类型
     */
    private String paperType;

    /**
     * 试卷来源
     */
    private String sourcePaper;

    /**
     * 年份
     */
    private String year;

    /**
     * 区域
     */
    private String region;

    /**
     * 学科
     */
    private String subject;

    /**
     * 标签
     */
    private String tag;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 知识点ID列表（直接关联的知识点）
     */
    private List<String> knowledgeTreeIds;

    /**
     * 知识点名称列表（直接关联的知识点名称）
     */
    private List<String> knowledgeTreeNames;

    /**
     * 所有相关知识点ID列表（包含父级知识点）
     */
    private List<String> allKnowledgeTreeIds;

    /**
     * 所有相关知识点名称列表（包含父级知识点名称）
     */
    private List<String> allKnowledgeTreeNames;

    /**
     * 试卷名称
     */
    private String paperName;

    /**
     * 题目在试卷中的序号
     */
    private Integer questionIndex;

    /**
     * 题目分类 0-题目 1-答案或解析
     */
    private Integer category;

    /**
     * 题目坐标信息(用于图片定位)
     */
    private Double x1;
    private Double y1;
    private Double y4;
}
