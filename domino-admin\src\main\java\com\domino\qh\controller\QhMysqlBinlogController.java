package com.domino.qh.controller;

import com.domino.common.annotation.Anonymous;
import org.springframework.web.bind.annotation.*;

@RestController
public class QhMysqlBinlogController {

    @Anonymous
    @PostMapping("/mysql/quQuestionBank/insert")
    public String insert(@RequestParam(name = "data") String data,
                         @RequestHeader(value="Authorization") String authorization) {
        System.out.println("method: post");
        System.out.println("authorization: "+authorization);
        System.out.println("data: "+data);
        return "ok";
    }

    @Anonymous
    @PutMapping("/mysql/quQuestionBank/update")
    public String update(@RequestParam(name = "data") String data,
                         @RequestHeader(value="Authorization") String authorization) {
        System.out.println("method: put");
        System.out.println("authorization: "+authorization);
        System.out.println("data: "+data);
        return "ok";
    }

    @Anonymous
    @DeleteMapping("/mysql/quQuestionBank/delete/{id}")
    public String delete(@PathVariable(name = "id") String id,
                         @RequestHeader(value="Authorization") String authorization) {
        System.out.println("method: delete");
        System.out.println("authorization: "+authorization);
        System.out.println("id: "+id);
        return "ok";
    }
}
