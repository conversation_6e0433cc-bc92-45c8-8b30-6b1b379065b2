package com.domino.qh.service;

import com.domino.common.qh.domain.QhQuestionBank;
import com.domino.common.qh.dto.QhPaperAnalysisDTO;

import java.util.List;

/**
 * 试卷分析服务接口
 */
public interface IQhPaperAnalysisService {
    
    /**
     * 生成试卷分析报告
     * @param paperId 试卷ID
     * @param questionBanks 试卷包含的题目列表
     * @return 试卷分析报告DTO
     */
    QhPaperAnalysisDTO generatePaperAnalysisReport(String paperId, List<QhQuestionBank> questionBanks);
} 