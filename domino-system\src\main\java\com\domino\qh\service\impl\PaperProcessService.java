package com.domino.qh.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aspose.words.*;
import com.domino.common.core.redis.RedisCache;
import com.domino.common.enums.QhPaperProcessType;
import com.domino.common.event.QuestionEventPublisher;
import com.domino.common.lib.CrackApose;
import com.domino.common.qh.domain.QhQuestionBank;
import com.domino.common.qh.dto.QhProcessPaperDTO;
import com.domino.common.qh.dto.QhProcessPaperDetailDTO;
import com.domino.common.qh.dto.QhProcessPaperFileDTO;
import com.domino.common.qh.dto.QhProcessPaperHistoryDTO;
import com.domino.common.utils.SecurityUtils;
import com.domino.qh.mapper.QhExamPaperMapper;
import com.domino.qh.mapper.QhQuestionBankMapper;
import com.domino.qh.service.MinioService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PaperProcessService {
    @Value("${paper.storage.path:./papers}")
    private String storagePath;
    @Resource
    private MinioService minioService;
    @Resource
    private RedisCache redisCache;
    @Resource
    private QhExamPaperMapper qhExamPaperMapper;
    @Resource
    private QhQuestionBankMapper qhQuestionBankMapper;
    @Resource
    private QuestionEventPublisher questionEventPublisher;

    // 假设屏幕高度为1080像素
    private static final int SCREEN_HEIGHT = 1080 * 3;

    @Async("DominoThreadPool")
    public void processPaper(Long userId, MultipartFile file) throws Exception {
        // 去掉.docx后缀
        int lastDotIndex = Objects.requireNonNull(file.getOriginalFilename()).lastIndexOf(".");
        String paperName = lastDotIndex > 0 ? file.getOriginalFilename().substring(0, lastDotIndex) : file.getOriginalFilename();
        // 添加时间戳为后缀，做区分
        paperName += ":" + System.currentTimeMillis();
        String statusKey = "processPaper:" + userId + ":" + paperName;
        String fileName = file.getOriginalFilename();
        String url = minioService.fileUpload(fileName, file.getInputStream());
        redisCache.setCacheMapValue(statusKey, "status", "processing");
        redisCache.setCacheMapValue(statusKey, "tmp", System.currentTimeMillis());
        redisCache.setCacheMapValue(statusKey, "url", url);

        String fileId = UUID.randomUUID().toString();
        String extension = Objects.requireNonNull(fileName).substring(fileName.lastIndexOf("."));
        Path tempFilePath = Files.createTempFile(fileId, extension);
        File tempFile = tempFilePath.toFile();
        file.transferTo(tempFilePath);

        QhProcessPaperDTO paper = new QhProcessPaperDTO();
        List<QhProcessPaperDetailDTO> details = new ArrayList<>();

        try {
            // 加载 Word 文档
            FileInputStream fis = new FileInputStream(tempFile);
            Document doc = new Document(fis);

            // 处理架构 暂不处理
            extractQuestions(details, paper, doc);

            // 处理图片
            CrackApose.initLicense();
            List<QhProcessPaperFileDTO> result = convertWordToImageAndOCR(tempFile);

            // 解析图片
            processOcrResults(result, paper);

            // 清理图片
            clearImgFiles(result);

            paper.setPaperName(paperName);
            // 如果任务被取消了，就不要存解析结果了
            if (Objects.nonNull(redisCache.getCacheList(statusKey))) {
                // 设置状态: 解析成功
                redisCache.setCacheMapValue(statusKey, "status", "success");
                redisCache.setCacheMapValue(statusKey, "result", JSONObject.toJSONString(paper));
            }
            // return paper;
        } catch (Exception e) {
            // 设置状态: 解析失败
            redisCache.setCacheMapValue(statusKey, "status", "error");
            log.error("处理试卷出错", e);
        } finally {
            tempFile.delete();
        }
        // return null;
    }

    private void clearImgFiles(List<QhProcessPaperFileDTO> results) {
        for (QhProcessPaperFileDTO result : results) {
            if (result.getFilePath() != null && result.getFilePath().length() > 0) {
                File file = new File(result.getFilePath());
                if (file.exists()) {
                    file.delete();
                }
            }
        }
    }

    public List<QhProcessPaperFileDTO> convertWordToImageAndOCR(File wordFile) throws Exception {
        List<QhProcessPaperFileDTO> ocrResults = new ArrayList<>();

        // 1. 加载Word文档并转换为图片
        Document doc = new Document(new FileInputStream(wordFile));
        ImageSaveOptions options = new ImageSaveOptions(SaveFormat.PNG);
        options.setResolution(300);
        options.setUseHighQualityRendering(true);

        String tempDir = System.getProperty("java.io.tmpdir");
        File[] pageImages = new File[doc.getPageCount()];

        try {
            // 2. 保存每一页为临时图片
            for (int pageIndex = 0; pageIndex < doc.getPageCount(); pageIndex++) {
                String tempImagePath = tempDir + "/word_page_" + pageIndex + ".png";
                options.setPageSet(new PageSet(pageIndex));
                doc.save(tempImagePath, options);
                pageImages[pageIndex] = new File(tempImagePath);
            }

            // 3. 合并所有图片为一张大图
            BufferedImage[] images = new BufferedImage[pageImages.length];
            int totalWidth = 0, totalHeight = 0;

            for (int i = 0; i < pageImages.length; i++) {
                images[i] = ImageIO.read(pageImages[i]);
                if (i == 0) totalWidth = images[i].getWidth();
                totalHeight += images[i].getHeight();
            }

            BufferedImage combinedImage = new BufferedImage(totalWidth, totalHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = combinedImage.createGraphics();
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, totalWidth, totalHeight);

            int currentHeight = 0;
            for (BufferedImage image : images) {
                g2d.drawImage(image, 0, currentHeight, null);
                currentHeight += image.getHeight();
            }
            g2d.dispose();

            // 4. 按屏幕高度切割图片并处理
            int startY = 0;
            int partIndex = 0;
            while (startY < totalHeight) {
                int partHeight = Math.min(SCREEN_HEIGHT, totalHeight - startY);
                BufferedImage partImage = combinedImage.getSubimage(0, startY, totalWidth, partHeight);

                // 保存切割后的图片
                String fileId = UUID.randomUUID().toString();
                String fileName = fileId + "_part_" + partIndex + ".png";
                File partFile = new File(fileName);
                ImageIO.write(partImage, "PNG", partFile);


                // 调用OCR接口
                QhProcessPaperFileDTO ocrResult = new QhProcessPaperFileDTO();
                ocrResult.setFilePath(fileName);
                ocrResult.setOcrResult(callOCRWithOkHttp(partFile));
                ocrResults.add(ocrResult);

                startY += partHeight;
                partIndex++;
            }

        } finally {
            for (File tempFile : pageImages) {
                if (tempFile != null && tempFile.exists()) {
                    tempFile.delete();
                }
            }
        }

        return ocrResults;
    }

    // 发送OCR请求并返回结果
    private String callOCRWithOkHttp(File imageFile) throws IOException {
        OkHttpClient client = new OkHttpClient();

        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("file", imageFile.getName(),
                        RequestBody.create(MediaType.parse("image/png"), imageFile))
                .build();

        Request request = new Request.Builder()
                .url("http://dpp183.site:34115/ocr/image")
                .post(requestBody)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("OCR请求失败: " + response.code());
            }
            return response.body().string();
        }
    }

    private void processOcrResults(List<QhProcessPaperFileDTO> ocrResults, QhProcessPaperDTO paper) {
        List<QhProcessPaperDetailDTO> totalDetails = new ArrayList<>();
        Integer nowType = null;
        Integer lastType = null;
        boolean answerFlag = false;
        // 问题的索引
        int count = 1;

        for (QhProcessPaperFileDTO ocrResult : ocrResults) {
            List<QhProcessPaperDetailDTO> tempDetails = new ArrayList<>();

            String filePath = ocrResult.getFilePath();
            String ocrResult1 = ocrResult.getOcrResult();
            JSONObject resultJson = JSON.parseObject(ocrResult1);
            if (resultJson.getInteger("code") != 200) {
                continue;
            }

            // 加载原始图片
            BufferedImage sourceImage;
            try {
                sourceImage = ImageIO.read(new File(filePath));
            } catch (IOException e) {
                log.error("加载图片失败: {}", filePath, e);
                continue;
            }

            JSONObject data = resultJson.getJSONObject("data");
            JSONArray textArray = data.getJSONArray("text");
            JSONArray boxesArray = data.getJSONArray("boxes");

            for (int i = 0; i < textArray.size(); i++) {
                QhProcessPaperDetailDTO detail = new QhProcessPaperDetailDTO();

                String text = textArray.getString(i);
                JSONArray box = boxesArray.getJSONArray(i);

                detail.setOcrText(text);
                detail.setX1(box.getDouble(0));
                detail.setY1(box.getDouble(1));
                detail.setY4(box.getDouble(7));

                nowType = QhPaperProcessType.getType(text, nowType);

                if (lastType == null || !lastType.equals(nowType)) {
                    lastType = nowType;
                    continue;
                }

                detail.setQuestionType(String.valueOf(lastType));

                // 题目
                if (checkIsQuestion(text)) {
                    detail.setCategory(0);
                } else { // 答案或解析
                    if (!answerFlag) {
                        if (!checkIsAnswer(text)) {
                            continue;
                        }
                    }

                    if (checkIsDA(text) || checkIsJD(text)) {
                        answerFlag = true;
                        detail.setCategory(1);
                    } else {
                        continue;
                    }
                }

                detail.setText(text);
                totalDetails.add(detail);
                tempDetails.add(detail);
            }

            int index = 0;
            for (QhProcessPaperDetailDTO detail : tempDetails) {
                try {
                    Double nextY = tempDetails.size() - 1 == index ? 0 : tempDetails.get(index + 1).getY1();
                    createQuestion(count, nextY, detail, sourceImage, storagePath + "/" + paper.getPaperName());
                    index++;
                    count++;
                } catch (Exception e) {
                    log.error("处理图片失败", e);
                }
            }
        }

        paper.setDetails(totalDetails);
    }

    private void createQuestion(int index, Double nextY, QhProcessPaperDetailDTO detail, BufferedImage sourceImage, String outputDir) {
        // 1. 计算截取区域（确保在图片范围内）
        int x = detail.getX1().intValue();
        int y = detail.getY1().intValue();
        int width = Math.max(sourceImage.getWidth() - x, 1900);
        int height = (int) (nextY == 0 ? sourceImage.getHeight() - y : nextY - y);

        BufferedImage questionImage = sourceImage.getSubimage(x, y, width, height);

        // 2. 保存图片文件
        String imageName = index + "_question.png";
        try {
            // 将 BufferedImage 转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(questionImage, "png", baos);
            byte[] imageBytes = baos.toByteArray();

            // 创建 InputStream
            InputStream inputStream = new ByteArrayInputStream(imageBytes);

            // 上传到 MinIO
            String url = minioService.fileUpload(imageName, inputStream);
            detail.setContext(url);
            log.info("题目图片已上传到 MinIO: {}", url);
        } catch (IOException e) {
            log.error("处理题目图片失败", e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void extractQuestions(List<QhProcessPaperDetailDTO> details, QhProcessPaperDTO paper, Document doc) {
        int pageCount = 1;
        NodeCollection<Paragraph> paragraphs = doc.getChildNodes(NodeType.PARAGRAPH, true);
        Integer nowQuestion = null;
        Integer lastType = null;
        for (Paragraph para : paragraphs) {
            if (pageCount == 1) {
                pageCount++;
                continue;
            }

            String text = para.getText();
            QhProcessPaperDetailDTO detail = new QhProcessPaperDetailDTO();

            if (pageCount == 2) {
                // 正则去掉换行符
                text = text.replaceAll("\\s+", "");
                paper.setPaperName(text);
                pageCount++;
                continue;
            }

            nowQuestion = QhPaperProcessType.getType(text, nowQuestion);
            detail.setQuestionType(String.valueOf(nowQuestion));

            if (lastType == null || !lastType.equals(nowQuestion)) {
                lastType = nowQuestion;
                continue;
            }

            // 匹配是否是题目
            Boolean isQuestion = checkIsQuestion(text);
            detail.setOcrText(text);
            if (isQuestion) {
                detail.setText(text);
                detail.setQuestionType("0");
            } else {
                // 答案不连续记录
                if ("1".equals(details.get(details.size() - 1).getQuestionType())) {
                    continue;
                }
                detail.setText(text);
                detail.setQuestionType("1");
            }

            details.add(detail);

            pageCount++;
            System.out.println("段落内容: " + para.getText());
        }
        paper.setDetails(details);
    }

    private Boolean checkIsDA(String text) {
        return "答案".contains(text);
    }

    private Boolean checkIsJD(String text) {
        return "解答".contains(text);
    }

    private Boolean checkIsAnswer(String text) {
        Pattern pattern = Pattern.compile("^.*(答案.*解析|解析.*答案).*$");
        Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            System.out.println("匹配成功-答案: " + text);
            return true;
        }

        return false;
    }

    private Boolean checkIsQuestion(String text) {
        Pattern pattern = Pattern.compile(
                "^(.*?[^\\d]|^)" +          // 前面可以有非数字内容或字符串开头
                        "(\\d+)\\s*[．.、]\\s*" +    // 题号（如"19"）和分隔符（．.、）
                        "[（\\(]\\s*(\\d+)\\s*分[）\\)]" +  // 分数部分，如（9分）
                        "(?![^\\s（\\(]*分)"         // 确保后面不会再出现"分"字
        );
        Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            System.out.println("匹配成功-题目: " + text);
            return true;
        }

        return false;
    }

    public List<QhProcessPaperHistoryDTO> processStatus() {
        String prefix = "processPaper:" + SecurityUtils.getUserId() + ":";
        String statusKey = prefix + "*";

        return redisCache.keys(statusKey).stream()
                .map(key -> new AbstractMap.SimpleEntry<>(
                        key.replace(prefix, ""),
                        redisCache.getCacheMap(key)
                ))
                .filter(entry -> entry.getValue() != null)
                // 转换为QhProcessPaperHistoryDTO对象
                .map(entry -> {
                    Map<String, Object> map = entry.getValue();
                    QhProcessPaperHistoryDTO dto = new QhProcessPaperHistoryDTO();

                    // 设置试卷名称
                    dto.setTitle(entry.getKey());

                    // 设置上传时间（使用tmp字段的时间戳）
                    if (map.get("tmp") != null) {
                        long timestamp = Long.parseLong(map.get("tmp").toString());
                        dto.setUploadTime(new Date(timestamp));
                    }

                    // 设置解析状态
                    dto.setStatus(map.get("status") != null ? map.get("status").toString() : "");
                    // 解析试卷url
                    dto.setUrl(map.get("url") != null ? map.get("url").toString() : "");
                    return dto;
                })
                // 按上传时间降序排序
                .sorted((dto1, dto2) -> {
                    Date time1 = dto1.getUploadTime();
                    Date time2 = dto2.getUploadTime();
                    if (time1 == null) return 1;
                    if (time2 == null) return -1;
                    return time2.compareTo(time1); // 最新的在前
                })
                .collect(Collectors.toList());
    }

    public void delete(String fileName) {

        String statusKey = "processPaper:" + SecurityUtils.getUserId() + ":" + fileName;

        // 如果试卷状态是finished，则说明已经录入题库
        if ("finished".equals(redisCache.getCacheMapValue(statusKey, "status"))) {
            Object result = redisCache.getCacheMapValue(statusKey, "result");
            QhProcessPaperDTO paper = JSONObject.parseObject(result.toString(), QhProcessPaperDTO.class);
            // 删除实际试卷
            qhExamPaperMapper.deleteQhExamPaperById(paper.getId());
            QhQuestionBank questionBank = new QhQuestionBank();
            questionBank.setSourcePaper(paper.getId());
            List<QhQuestionBank> qhQuestionBanks = qhQuestionBankMapper.selectBankList(questionBank);
            // 删除题目
            qhQuestionBankMapper.deleteQuestionBankByPaperId(paper.getId(), String.valueOf(SecurityUtils.getUserId()));
            // 删除ES
            questionEventPublisher.publishBatchDeleteEvent(qhQuestionBanks.stream().map(QhQuestionBank::getId).collect(Collectors.toList()));
        }

        // 删除历史缓存
        redisCache.deleteObject(statusKey);
    }
}
